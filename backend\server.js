const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

const { testConnection } = require('./config/database');

// 导入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const repairRoutes = require('./routes/repairs');
const announcementRoutes = require('./routes/announcements');
const dataRoutes = require('./routes/data');

const app = express();
const PORT = process.env.PORT || 3001;

// 安全中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  }
});
app.use('/api/', limiter);

// CORS配置
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// 解析JSON和URL编码的数据
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/repairs', repairRoutes);
app.use('/api/announcements', announcementRoutes);
app.use('/api/data', dataRoutes);

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: '易宿管后端服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API文档端点
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: '易宿管智能宿舍管理系统 API',
    version: '1.0.0',
    endpoints: {
      auth: {
        'POST /api/auth/login': '用户登录',
        'POST /api/auth/register': '用户注册',
        'GET /api/auth/me': '获取当前用户信息'
      },
      users: {
        'GET /api/users': '获取用户列表',
        'POST /api/users': '创建用户',
        'PUT /api/users/:id': '更新用户',
        'DELETE /api/users/:id': '删除用户'
      },
      repairs: {
        'GET /api/repairs': '获取维修请求列表',
        'GET /api/repairs/:id': '获取维修请求详情',
        'POST /api/repairs': '创建维修请求',
        'PUT /api/repairs/:id/status': '更新维修状态'
      },
      announcements: {
        'GET /api/announcements': '获取公告列表',
        'GET /api/announcements/:id': '获取公告详情',
        'POST /api/announcements': '创建公告',
        'DELETE /api/announcements/:id': '删除公告'
      },
      data: {
        'GET /api/data/colleges': '获取学院列表',
        'GET /api/data/majors': '获取专业列表',
        'GET /api/data/dorm-buildings': '获取宿舍楼列表',
        'GET /api/data/rooms': '获取房间列表',
        'GET /api/data/beds': '获取床位列表',
        'GET /api/data/utility-bills': '获取水电费账单',
        'GET /api/data/room-occupancy': '获取房间占用情况'
      }
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '请求的资源不存在'
  });
});

// 全局错误处理中间件
app.use((error, req, res, next) => {
  console.error('全局错误:', error);
  
  // 数据库连接错误
  if (error.code === 'ECONNREFUSED') {
    return res.status(503).json({
      success: false,
      message: '数据库连接失败'
    });
  }
  
  // JWT错误
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: '无效的访问令牌'
    });
  }
  
  // 验证错误
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: '数据验证失败',
      errors: error.details
    });
  }
  
  // 默认服务器错误
  res.status(500).json({
    success: false,
    message: process.env.NODE_ENV === 'production' ? '服务器内部错误' : error.message
  });
});

// 启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log('🚀 易宿管后端服务启动成功!');
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`📖 API文档: http://localhost:${PORT}/api`);
      console.log(`🏥 健康检查: http://localhost:${PORT}/api/health`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log('='.repeat(50));
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 启动服务器
startServer();
