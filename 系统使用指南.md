# 🏠 易宿管智能宿舍管理系统 - 完整使用指南

## 🎯 系统概述

易宿管是一个现代化的智能宿舍管理系统，提供完整的宿舍生活管理解决方案，包括维修申请、公告发布、用户管理、房间分配等功能。

---

## 🚀 快速开始

### 1. 系统启动

#### 启动后端服务
```bash
cd backend
npm start
```
- 服务地址: http://localhost:3002
- API文档: http://localhost:3002/api

#### 启动前端服务
```bash
cd qian
npm run dev
```
- 访问地址: http://localhost:5173

### 2. 系统登录

访问 http://localhost:5173，使用以下测试账户登录：

#### 🔑 测试账户
| 角色 | 邮箱 | 密码 | 权限 |
|------|------|------|------|
| 系统管理员 | <EMAIL> | password123 | 全部功能 |
| 宿舍管理员 | <EMAIL> | password123 | 宿舍管理 |
| 学生 | <EMAIL> | password123 | 基础功能 |
| 维修人员 | <EMAIL> | password123 | 维修处理 |

---

## 👥 角色功能详解

### 🎓 学生功能

#### 1. 提交维修申请
- 登录后进入"报修申请"页面
- 点击"提交报修"按钮
- 填写问题描述、联系方式等信息
- 可上传图片链接作为问题说明
- 提交后可查看处理进度

#### 2. 查看维修状态
- 在维修列表中查看自己的所有申请
- 状态包括：待处理、已指派、维修中、已完成、已确认
- 点击"查看详情"可看到完整的处理记录

#### 3. 确认维修完成
- 当维修状态为"已完成"时
- 学生可点击"确认完成"按钮
- 确认后状态变为"已确认"

#### 4. 查看公告
- 可查看全校公告、学院公告、宿舍楼公告
- 按时间倒序显示最新公告

#### 5. 查看水电费
- 查看个人水电费账单
- 按月份显示用量和费用
- 查看缴费状态

### 🏢 宿舍管理员功能

#### 1. 维修管理
- 查看所管理宿舍楼的所有维修申请
- 将待处理的维修申请指派给维修人员
- 跟踪维修进度

#### 2. 指派维修任务
- 在维修列表中点击"指派"按钮
- 从维修人员列表中选择合适的人员
- 系统自动发送指派通知

#### 3. 用户管理
- 查看所管理宿舍楼的学生信息
- 查看维修人员列表

#### 4. 公告发布
- 发布宿舍楼范围的公告
- 设置公告标题和内容
- 选择发布范围

#### 5. 房间管理
- 查看房间占用情况
- 管理床位分配
- 查看入住统计

### 🔧 维修人员功能

#### 1. 接收维修任务
- 查看指派给自己的维修任务
- 查看待处理的维修申请

#### 2. 更新维修状态
- 将任务状态更新为"维修中"
- 完成后更新为"已完成"
- 添加维修备注和说明

#### 3. 维修记录
- 查看历史维修记录
- 添加维修过程说明

### 👑 系统管理员功能

#### 1. 用户管理
- 创建、编辑、删除用户
- 分配用户角色和权限
- 管理用户基本信息

#### 2. 系统配置
- 管理学院和专业信息
- 配置宿舍楼和房间
- 设置系统参数

#### 3. 全局公告
- 发布全校范围公告
- 发布学院范围公告
- 管理所有公告

#### 4. 数据统计
- 查看系统使用统计
- 维修请求统计
- 用户活跃度统计

---

## 📊 功能模块详解

### 🔧 维修管理流程

1. **学生提交** → 2. **管理员指派** → 3. **维修人员处理** → 4. **学生确认**

#### 状态说明
- **待处理**: 学生刚提交，等待指派
- **已指派**: 已分配给维修人员
- **维修中**: 维修人员正在处理
- **已完成**: 维修人员完成工作
- **已确认**: 学生确认满意
- **已取消**: 申请被取消

### 📢 公告系统

#### 发布范围
- **全体**: 所有用户可见
- **学院**: 指定学院用户可见
- **宿舍楼**: 指定宿舍楼用户可见

#### 权限控制
- 系统管理员：可发布所有范围公告
- 宿舍管理员：只能发布自己管理的宿舍楼公告

### 🏠 房间管理

#### 房间类型
- 单人间 (1人)
- 双人间 (2人)
- 四人间 (4人)
- 六人间 (6人)

#### 床位状态
- **空闲**: 可分配
- **已入住**: 已有学生

---

## 🛠️ 技术特性

### 🔐 安全特性
- JWT身份认证
- 密码加密存储
- 权限分级控制
- CORS跨域保护

### 📱 响应式设计
- 支持桌面端和移动端
- 自适应布局
- 现代化UI设计

### ⚡ 性能优化
- 数据库连接池
- 查询索引优化
- 前端组件懒加载

---

## 🔍 故障排除

### 常见问题

#### 1. 无法登录
- 检查邮箱和密码是否正确
- 确认后端服务是否启动
- 检查网络连接

#### 2. 数据加载失败
- 检查后端API服务状态
- 查看浏览器控制台错误信息
- 确认数据库连接正常

#### 3. 权限不足
- 确认当前用户角色
- 检查功能权限要求
- 联系系统管理员

### 技术支持

#### 服务状态检查
- 后端健康检查: http://localhost:3002/api/health
- API文档: http://localhost:3002/api
- 前端访问: http://localhost:5173

#### 日志查看
- 后端日志：查看终端输出
- 前端日志：浏览器开发者工具
- 数据库日志：MySQL错误日志

---

## 📞 联系支持

如遇到问题，请：
1. 查看本使用指南
2. 检查系统状态
3. 查看错误日志
4. 联系技术支持

---

## 🎉 开始使用

现在您可以：
1. 启动系统服务
2. 使用测试账户登录
3. 体验完整功能
4. 根据需要配置系统

**祝您使用愉快！** 🚀
