# 易宿管智能宿舍管理系统数据库使用说明

## 数据库信息
- **数据库名称**: `redhat`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **MySQL版本**: 5.7.26+

## 数据库已成功创建并导入！

✅ **数据库创建状态**: 成功  
✅ **数据导入状态**: 完成  
✅ **表结构创建**: 17个表  
✅ **初始数据**: 已导入测试数据  

## 数据库表结构

### 1. 基础数据表
- `colleges` - 学院表 (3条记录)
- `majors` - 专业表 (5条记录)
- `dorm_buildings` - 宿舍楼表 (3条记录)

### 2. 用户管理表
- `users` - 用户表 (7条记录)
  - 1个系统管理员
  - 2个宿舍管理员
  - 3个学生
  - 1个维修人员

### 3. 宿舍管理表
- `rooms` - 房间表 (4条记录)
- `beds` - 床位表 (15条记录)

### 4. 业务功能表
- `repair_requests` - 维修请求表 (3条记录)
- `repair_updates` - 维修更新记录表 (3条记录)
- `announcements` - 公告表 (3条记录)
- `utility_bills` - 水电费表 (6条记录)
- `late_returns` - 晚归记录表 (3条记录)
- `visitors` - 访客记录表 (3条记录)
- `violations` - 违规记录表 (3条记录)
- `civilized_dorm_scores` - 文明宿舍评分表 (6条记录)

### 5. 视图表
- `user_details` - 用户详细信息视图
- `room_occupancy` - 房间占用情况视图
- `repair_request_details` - 维修请求详情视图

## 默认测试账户

### 系统管理员
- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **角色**: 系统管理员

### 宿舍管理员
- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **角色**: 宿舍管理员 (A栋)

- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **角色**: 宿舍管理员 (B栋)

### 学生账户
- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **角色**: 学生 (王五 - A栋101)

- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **角色**: 学生 (赵六 - B栋205)

- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **角色**: 学生 (孙七 - A栋102)

### 维修人员
- **邮箱**: `<EMAIL>`
- **密码**: `password123`
- **角色**: 维修人员

## 数据库连接信息

```
主机: localhost
端口: 3306
数据库名: redhat
用户名: root
密码: root
```

## 常用查询示例

### 查看所有表
```sql
USE redhat;
SHOW TABLES;
```

### 查看用户信息
```sql
SELECT name, role, email FROM users;
```

### 查看房间占用情况
```sql
SELECT * FROM room_occupancy;
```

### 查看维修请求
```sql
SELECT student_name, room_number, description, status FROM repair_requests;
```

### 查看水电费账单
```sql
SELECT u.name, ub.month, ub.total_cost, ub.is_paid 
FROM utility_bills ub 
JOIN users u ON ub.student_id = u.id;
```

## 数据库特性

### 1. 外键约束
- 所有表都设置了适当的外键约束
- 支持级联删除和更新

### 2. 索引优化
- 为常用查询字段创建了索引
- 提高查询性能

### 3. 触发器
- 自动更新房间占用床位数
- 保证数据一致性

### 4. 数据完整性
- 使用ENUM类型限制状态值
- 设置CHECK约束验证数据

## 备份与恢复

### 备份数据库
```bash
mysqldump -u root -p redhat > redhat_backup.sql
```

### 恢复数据库
```bash
mysql -u root -p redhat < redhat_backup.sql
```

## 注意事项

1. **密码安全**: 生产环境中请修改默认密码
2. **数据备份**: 定期备份数据库
3. **权限管理**: 根据需要设置用户权限
4. **性能监控**: 监控数据库性能和查询效率

## 技术支持

如有问题，请检查：
1. MySQL服务是否正常运行
2. 数据库连接参数是否正确
3. 用户权限是否足够
4. 表结构是否完整

---

**数据库创建完成时间**: 2025-06-15  
**版本**: v1.0  
**状态**: ✅ 可用
