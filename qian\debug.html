<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>易宿管API调试页面</h1>
    
    <div>
        <button onclick="testHealth()">测试健康检查</button>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testCORS()">测试CORS</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:3002/api';
        
        function addResult(title, content, isSuccess = true) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(content, null, 2)}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testHealth() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                addResult('健康检查', { status: response.status, data }, response.ok);
            } catch (error) {
                addResult('健康检查', { error: error.message }, false);
            }
        }
        
        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                const data = await response.json();
                addResult('登录测试', { status: response.status, data }, response.ok);
            } catch (error) {
                addResult('登录测试', { error: error.message }, false);
            }
        }
        
        async function testCORS() {
            try {
                const response = await fetch(`${API_BASE_URL}/`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'http://localhost:5173',
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                addResult('CORS测试', { 
                    status: response.status, 
                    headers: Object.fromEntries(response.headers.entries())
                }, response.ok);
            } catch (error) {
                addResult('CORS测试', { error: error.message }, false);
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
