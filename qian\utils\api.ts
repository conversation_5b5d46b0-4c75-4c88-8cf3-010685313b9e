// API配置和请求工具
const API_BASE_URL = 'http://localhost:3002/api';

// 获取存储的token
const getToken = (): string | null => {
  return localStorage.getItem('token');
};

// 设置token
const setToken = (token: string): void => {
  localStorage.setItem('token', token);
};

// 移除token
const removeToken = (): void => {
  localStorage.removeItem('token');
};

// 通用请求函数
const apiRequest = async (
  endpoint: string,
  options: RequestInit = {}
): Promise<any> => {
  const url = `${API_BASE_URL}${endpoint}`;
  const token = getToken();

  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  };

  if (token) {
    defaultHeaders.Authorization = `Bearer ${token}`;
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      // 如果是401错误，清除token并重定向到登录页
      if (response.status === 401) {
        removeToken();
        window.location.href = '/login';
      }
      throw new Error(data.message || '请求失败');
    }

    return data;
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
};

// GET请求
const get = (endpoint: string): Promise<any> => {
  return apiRequest(endpoint, { method: 'GET' });
};

// POST请求
const post = (endpoint: string, data?: any): Promise<any> => {
  return apiRequest(endpoint, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
};

// PUT请求
const put = (endpoint: string, data?: any): Promise<any> => {
  return apiRequest(endpoint, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  });
};

// DELETE请求
const del = (endpoint: string): Promise<any> => {
  return apiRequest(endpoint, { method: 'DELETE' });
};

// 认证相关API
export const authAPI = {
  // 登录
  login: (email: string, password: string) =>
    post('/auth/login', { email, password }),
  
  // 注册
  register: (userData: any) =>
    post('/auth/register', userData),
  
  // 获取当前用户信息
  getCurrentUser: () =>
    get('/auth/me'),
};

// 用户管理API
export const userAPI = {
  // 获取用户列表
  getUsers: (params?: any) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return get(`/users${query}`);
  },
  
  // 创建用户
  createUser: (userData: any) =>
    post('/users', userData),
  
  // 更新用户
  updateUser: (id: string, userData: any) =>
    put(`/users/${id}`, userData),
  
  // 删除用户
  deleteUser: (id: string) =>
    del(`/users/${id}`),
};

// 维修请求API
export const repairAPI = {
  // 获取维修请求列表
  getRepairRequests: (params?: any) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return get(`/repairs${query}`);
  },
  
  // 获取维修请求详情
  getRepairRequest: (id: string) =>
    get(`/repairs/${id}`),
  
  // 创建维修请求
  createRepairRequest: (repairData: any) =>
    post('/repairs', repairData),
  
  // 更新维修状态
  updateRepairStatus: (id: string, statusData: any) =>
    put(`/repairs/${id}/status`, statusData),
};

// 公告API
export const announcementAPI = {
  // 获取公告列表
  getAnnouncements: (params?: any) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return get(`/announcements${query}`);
  },
  
  // 获取公告详情
  getAnnouncement: (id: string) =>
    get(`/announcements/${id}`),
  
  // 创建公告
  createAnnouncement: (announcementData: any) =>
    post('/announcements', announcementData),
  
  // 删除公告
  deleteAnnouncement: (id: string) =>
    del(`/announcements/${id}`),
};

// 基础数据API
export const dataAPI = {
  // 获取学院列表
  getColleges: () =>
    get('/data/colleges'),
  
  // 获取专业列表
  getMajors: (params?: any) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return get(`/data/majors${query}`);
  },
  
  // 获取宿舍楼列表
  getDormBuildings: () =>
    get('/data/dorm-buildings'),
  
  // 获取房间列表
  getRooms: (params?: any) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return get(`/data/rooms${query}`);
  },
  
  // 获取床位列表
  getBeds: (params?: any) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return get(`/data/beds${query}`);
  },
  
  // 获取水电费账单
  getUtilityBills: (params?: any) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return get(`/data/utility-bills${query}`);
  },
  
  // 获取房间占用情况
  getRoomOccupancy: () =>
    get('/data/room-occupancy'),
};

// 导出token相关函数
export { getToken, setToken, removeToken };

// 导出默认的API请求函数
export default {
  get,
  post,
  put,
  delete: del,
};
