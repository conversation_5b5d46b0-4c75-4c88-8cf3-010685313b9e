import React, { createContext, useState, useContext, ReactNode, useCallback } from 'react';
import { User, UserRole } from '../types';
import { authAPI, setToken, removeToken, getToken } from '../utils/api';

interface AuthContextType {
  currentUser: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: any) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const login = useCallback(async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await authAPI.login(email, password);

      if (response.success) {
        const { user, token } = response.data;

        // 设置token
        setToken(token);

        // 设置当前用户
        setCurrentUser(user);

        // 保存用户信息到localStorage
        localStorage.setItem('user', JSON.stringify(user));
      } else {
        throw new Error(response.message || '登录失败');
      }

    } catch (error: any) {
      console.error('登录错误:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const register = useCallback(async (userData: any) => {
    setIsLoading(true);
    try {
      const response = await authAPI.register(userData);

      if (response.success) {
        const { user, token } = response.data;

        // 设置token
        setToken(token);

        // 设置当前用户
        setCurrentUser(user);

        // 保存用户信息到localStorage
        localStorage.setItem('user', JSON.stringify(user));
      } else {
        throw new Error(response.message || '注册失败');
      }

    } catch (error: any) {
      console.error('注册错误:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(() => {
    setCurrentUser(null);
    removeToken();
    localStorage.removeItem('user');
  }, []);

  // 初始化时从localStorage恢复用户状态
  React.useEffect(() => {
    const initializeAuth = async () => {
      const savedUser = localStorage.getItem('user');
      const savedToken = getToken();

      if (savedUser && savedToken) {
        try {
          const user = JSON.parse(savedUser);

          // 验证token是否有效
          try {
            const response = await authAPI.getCurrentUser();
            if (response.success) {
              setCurrentUser(response.data.user);
            } else {
              throw new Error('Token无效');
            }
          } catch (error) {
            // Token无效，清除本地存储
            console.error('Token验证失败:', error);
            removeToken();
            localStorage.removeItem('user');
            setCurrentUser(null);
          }
        } catch (error) {
          console.error('解析保存的用户信息失败:', error);
          removeToken();
          localStorage.removeItem('user');
          setCurrentUser(null);
        }
      } else {
        // 没有保存的用户信息，确保状态为null
        setCurrentUser(null);
      }
    };

    initializeAuth();
  }, []);

  return (
    <AuthContext.Provider value={{ currentUser, login, register, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};