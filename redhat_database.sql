-- 易宿管智能宿舍管理系统数据库
-- 数据库名: redhat
-- 创建时间: 2025-06-15

-- 创建数据库
DROP DATABASE IF EXISTS redhat;
CREATE DATABASE redhat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE redhat;

-- 1. 学院表
CREATE TABLE colleges (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 专业表
CREATE TABLE majors (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    college_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE CASCADE,
    UNIQUE KEY unique_major_college (name, college_id)
);

-- 3. 宿舍楼表
CREATE TABLE dorm_buildings (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    floors INT NOT NULL DEFAULT 1,
    total_rooms INT NOT NULL DEFAULT 0,
    assigned_admin_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. 用户表
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('系统管理员', '宿舍管理员', '学生', '维修人员') NOT NULL,
    college VARCHAR(100),
    major VARCHAR(100),
    dorm_building VARCHAR(100),
    room_number VARCHAR(20),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_role (role),
    INDEX idx_email (email),
    INDEX idx_dorm_building (dorm_building)
);

-- 5. 房间表
CREATE TABLE rooms (
    id VARCHAR(50) PRIMARY KEY,
    room_number VARCHAR(20) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    floor INT NOT NULL,
    type ENUM('单人间', '双人间', '四人间', '六人间') NOT NULL,
    capacity INT NOT NULL,
    occupied_beds INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    UNIQUE KEY unique_room_building (room_number, dorm_building_id),
    INDEX idx_dorm_building (dorm_building_id),
    INDEX idx_floor (floor)
);

-- 6. 床位表
CREATE TABLE beds (
    id VARCHAR(50) PRIMARY KEY,
    room_id VARCHAR(50) NOT NULL,
    bed_number VARCHAR(10) NOT NULL,
    status ENUM('空闲', '已入住') NOT NULL DEFAULT '空闲',
    student_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_bed_room (bed_number, room_id),
    INDEX idx_room (room_id),
    INDEX idx_student (student_id),
    INDEX idx_status (status)
);

-- 7. 维修请求表
CREATE TABLE repair_requests (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    room_number VARCHAR(20) NOT NULL,
    dorm_building VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    image_url VARCHAR(500),
    contact VARCHAR(150) NOT NULL,
    status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') NOT NULL DEFAULT '待处理',
    submitted_at TIMESTAMP NOT NULL,
    assigned_to VARCHAR(50),
    assigned_to_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_student (student_id),
    INDEX idx_status (status),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_submitted_at (submitted_at)
);

-- 8. 维修更新记录表
CREATE TABLE repair_updates (
    id VARCHAR(50) PRIMARY KEY,
    repair_request_id VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    updated_by VARCHAR(100) NOT NULL,
    notes TEXT NOT NULL,
    new_status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repair_request_id) REFERENCES repair_requests(id) ON DELETE CASCADE,
    INDEX idx_repair_request (repair_request_id),
    INDEX idx_timestamp (timestamp)
);

-- 9. 公告表
CREATE TABLE announcements (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    author_id VARCHAR(50) NOT NULL,
    author_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    scope ENUM('All', 'College', 'DormBuilding') NOT NULL DEFAULT 'All',
    target_id VARCHAR(50),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_author (author_id),
    INDEX idx_scope (scope),
    INDEX idx_created_at (created_at)
);

-- 10. 水电费表
CREATE TABLE utility_bills (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    month VARCHAR(7) NOT NULL, -- YYYY-MM格式
    electricity_usage DECIMAL(10,2) NOT NULL DEFAULT 0,
    electricity_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    water_usage DECIMAL(10,2) NOT NULL DEFAULT 0,
    water_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_cost DECIMAL(10,2) NOT NULL DEFAULT 0,
    is_paid BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_month (student_id, month),
    INDEX idx_student (student_id),
    INDEX idx_room (room_id),
    INDEX idx_month (month),
    INDEX idx_is_paid (is_paid)
);

-- 11. 晚归记录表
CREATE TABLE late_returns (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    time TIME NOT NULL,
    reason TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_dorm_building (dorm_building_id),
    INDEX idx_date (date),
    INDEX idx_recorded_by (recorded_by)
);

-- 12. 访客记录表
CREATE TABLE visitors (
    id VARCHAR(50) PRIMARY KEY,
    visitor_name VARCHAR(100) NOT NULL,
    visitor_id_number VARCHAR(20) NOT NULL,
    reason TEXT,
    entry_time TIMESTAMP NOT NULL,
    exit_time TIMESTAMP,
    visited_student_id VARCHAR(50) NOT NULL,
    visited_student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (visited_student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_visited_student (visited_student_id),
    INDEX idx_dorm_building (dorm_building_id),
    INDEX idx_entry_time (entry_time),
    INDEX idx_recorded_by (recorded_by)
);

-- 13. 违规记录表
CREATE TABLE violations (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    action_taken TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_student (student_id),
    INDEX idx_dorm_building (dorm_building_id),
    INDEX idx_date (date),
    INDEX idx_type (type),
    INDEX idx_recorded_by (recorded_by)
);

-- 14. 文明宿舍评分表
CREATE TABLE civilized_dorm_scores (
    id VARCHAR(50) PRIMARY KEY,
    dorm_building_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    score INT NOT NULL CHECK (score >= 0 AND score <= 100),
    notes TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_room_date (room_id, date),
    INDEX idx_dorm_building (dorm_building_id),
    INDEX idx_room (room_id),
    INDEX idx_date (date),
    INDEX idx_score (score),
    INDEX idx_recorded_by (recorded_by)
);

-- ========================================
-- 插入初始数据
-- ========================================

-- 插入学院数据
INSERT INTO colleges (id, name) VALUES
('col01', '工程学院'),
('col02', '文理学院'),
('col03', '商学院');

-- 插入专业数据
INSERT INTO majors (id, name, college_id) VALUES
('maj01', '计算机科学', 'col01'),
('maj02', '机械工程', 'col01'),
('maj03', '历史学', 'col02'),
('maj04', '文学', 'col02'),
('maj05', '金融学', 'col03');

-- 插入宿舍楼数据
INSERT INTO dorm_buildings (id, name, floors, total_rooms, assigned_admin_id) VALUES
('bldgA', 'A栋 (阿尔法楼)', 5, 50, 'dormadmin01'),
('bldgB', 'B栋 (贝塔公寓)', 4, 40, 'dormadmin02'),
('bldgC', 'C栋 (伽马学舍)', 6, 60, NULL);

-- 插入用户数据
INSERT INTO users (id, name, email, password, phone, role, college, major, dorm_building, room_number, emergency_contact_name, emergency_contact_phone) VALUES
('sysadmin01', '系统管理员用户', '<EMAIL>', 'password123', NULL, '系统管理员', NULL, NULL, NULL, NULL, NULL, NULL),
('dormadmin01', '张三 (A栋宿舍管理员)', '<EMAIL>', 'password123', NULL, '宿舍管理员', NULL, NULL, 'A栋 (阿尔法楼)', NULL, NULL, NULL),
('dormadmin02', '李四 (B栋宿舍管理员)', '<EMAIL>', 'password123', NULL, '宿舍管理员', NULL, NULL, 'B栋 (贝塔公寓)', NULL, NULL, NULL),
('student01', '王五', '<EMAIL>', 'password123', '13000000001', '学生', '工程学院', '计算机科学', 'A栋 (阿尔法楼)', '101', '王大锤', '13800138000'),
('student02', '赵六', '<EMAIL>', 'password123', '13000000002', '学生', '文理学院', '历史学', 'B栋 (贝塔公寓)', '205', '赵铁柱', '13900139000'),
('student03', '孙七', '<EMAIL>', 'password123', '13000000003', '学生', '工程学院', '机械工程', 'A栋 (阿尔法楼)', '102', '孙悟空', '13700137000'),
('repair01', '维修工丁师傅', '<EMAIL>', 'password123', NULL, '维修人员', NULL, NULL, NULL, NULL, NULL, NULL);

-- 插入房间数据
INSERT INTO rooms (id, room_number, dorm_building_id, floor, type, capacity, occupied_beds) VALUES
('roomA101', '101', 'bldgA', 1, '六人间', 6, 1),
('roomA102', '102', 'bldgA', 1, '双人间', 2, 1),
('roomB205', '205', 'bldgB', 2, '六人间', 6, 1),
('roomC301', '301', 'bldgC', 3, '单人间', 1, 0);

-- 插入床位数据
INSERT INTO beds (id, room_id, bed_number, status, student_id) VALUES
-- Room A101 (六人间, student01 在床位1)
('bedA101-1', 'roomA101', '1', '已入住', 'student01'),
('bedA101-2', 'roomA101', '2', '空闲', NULL),
('bedA101-3', 'roomA101', '3', '空闲', NULL),
('bedA101-4', 'roomA101', '4', '空闲', NULL),
('bedA101-5', 'roomA101', '5', '空闲', NULL),
('bedA101-6', 'roomA101', '6', '空闲', NULL),
-- Room A102 (双人间, student03 在床位1)
('bedA102-1', 'roomA102', '1', '已入住', 'student03'),
('bedA102-2', 'roomA102', '2', '空闲', NULL),
-- Room B205 (六人间, student02 在床位1)
('bedB205-1', 'roomB205', '1', '已入住', 'student02'),
('bedB205-2', 'roomB205', '2', '空闲', NULL),
('bedB205-3', 'roomB205', '3', '空闲', NULL),
('bedB205-4', 'roomB205', '4', '空闲', NULL),
('bedB205-5', 'roomB205', '5', '空闲', NULL),
('bedB205-6', 'roomB205', '6', '空闲', NULL),
-- Room C301 (单人间, 空闲)
('bedC301-1', 'roomC301', '1', '空闲', NULL);

-- 插入维修请求数据
INSERT INTO repair_requests (id, student_id, student_name, room_number, dorm_building, description, contact, status, submitted_at, assigned_to, assigned_to_name, image_url) VALUES
('repair001', 'student01', '王五', '101', 'A栋 (阿尔法楼)', '卫生间水龙头漏水严重，无法关闭。', '<EMAIL>', '已指派', '2024-07-20 10:00:00', 'repair01', '维修工丁师傅', 'https://picsum.photos/seed/tap/300/200'),
('repair002', 'student02', '赵六', '205', 'B栋 (贝塔公寓)', '空调不制冷，需要检修。', '<EMAIL>', '待处理', '2024-07-21 14:30:00', NULL, NULL, NULL),
('repair003', 'student03', '孙七', '102', 'A栋 (阿尔法楼)', '门锁损坏，无法正常开关。', '<EMAIL>', '维修中', '2024-07-19 09:15:00', 'repair01', '维修工丁师傅', NULL);

-- 插入维修更新记录数据
INSERT INTO repair_updates (id, repair_request_id, timestamp, updated_by, notes, new_status) VALUES
('update001', 'repair001', '2024-07-20 11:00:00', '张三 (A栋宿舍管理员)', '已指派给丁师傅处理。', '已指派'),
('update002', 'repair003', '2024-07-19 10:00:00', '张三 (A栋宿舍管理员)', '已指派给丁师傅处理。', '已指派'),
('update003', 'repair003', '2024-07-19 15:30:00', '维修工丁师傅', '已到现场开始维修。', '维修中');

-- 插入公告数据
INSERT INTO announcements (id, title, content, author_id, author_name, scope, target_id) VALUES
('anno001', '近期维修安排通知', '请注意，A栋将于7月25日上午9点至下午5点进行计划内维修。', 'sysadmin01', '系统管理员', 'All', NULL),
('anno002', '宿舍会议 - B栋', 'B栋全体住宿学生务必参加7月26日晚上7点在公共活动室举行的会议。', 'dormadmin02', '李四', 'DormBuilding', 'bldgB'),
('anno003', '工程学院学生注意事项', '工程学院学生请注意实验室安全规定，严禁在宿舍内进行实验。', 'sysadmin01', '系统管理员', 'College', 'col01');

-- 插入水电费数据
INSERT INTO utility_bills (id, student_id, room_id, month, electricity_usage, electricity_cost, water_usage, water_cost, total_cost, is_paid) VALUES
('bill001', 'student01', 'roomA101', '2024-06', 50.00, 25.00, 5.00, 10.00, 35.00, TRUE),
('bill002', 'student02', 'roomB205', '2024-06', 60.00, 30.00, 6.00, 12.00, 42.00, FALSE),
('bill003', 'student01', 'roomA101', '2024-05', 45.00, 22.50, 4.00, 8.00, 30.50, FALSE),
('bill004', 'student03', 'roomA102', '2024-06', 30.00, 15.00, 3.00, 6.00, 21.00, TRUE),
('bill005', 'student02', 'roomB205', '2024-05', 55.00, 27.50, 5.50, 11.00, 38.50, TRUE),
('bill006', 'student03', 'roomA102', '2024-05', 28.00, 14.00, 2.80, 5.60, 19.60, TRUE);

-- 插入违规记录数据
INSERT INTO violations (id, student_id, student_name, dorm_building_id, date, type, description, action_taken, recorded_by) VALUES
('vio001', 'student01', '王五', 'bldgA', '2024-07-15', '违规电器', '宿舍内使用大功率吹风机', '口头警告', 'dormadmin01'),
('vio002', 'student02', '赵六', 'bldgB', '2024-07-16', '晚归', '超过规定时间返校', '记录一次', 'dormadmin02'),
('vio003', 'student03', '孙七', 'bldgA', '2024-07-18', '噪音扰民', '深夜播放音乐影响他人休息', '书面警告', 'dormadmin01');

-- 插入晚归记录数据
INSERT INTO late_returns (id, student_id, student_name, dorm_building_id, date, time, reason, recorded_by) VALUES
('lr001', 'student01', '王五', 'bldgA', '2024-07-18', '23:30:00', '图书馆学习', 'dormadmin01'),
('lr002', 'student03', '孙七', 'bldgA', '2024-07-19', '00:15:00', '社团活动', 'dormadmin01'),
('lr003', 'student02', '赵六', 'bldgB', '2024-07-20', '23:45:00', '兼职工作', 'dormadmin02');

-- 插入访客记录数据
INSERT INTO visitors (id, visitor_name, visitor_id_number, reason, entry_time, exit_time, visited_student_id, visited_student_name, dorm_building_id, recorded_by) VALUES
('vis001', '访客甲', '123456789012345678', '探亲', '2024-07-20 14:00:00', '2024-07-20 16:00:00', 'student01', '王五', 'bldgA', 'dormadmin01'),
('vis002', '访客乙', '987654321098765432', '送东西', '2024-07-21 10:00:00', NULL, 'student02', '赵六', 'bldgB', 'dormadmin02'),
('vis003', '访客丙', '456789123456789012', '学习讨论', '2024-07-19 19:00:00', '2024-07-19 21:30:00', 'student03', '孙七', 'bldgA', 'dormadmin01');

-- 插入文明宿舍评分数据
INSERT INTO civilized_dorm_scores (id, dorm_building_id, room_id, date, score, notes, recorded_by) VALUES
('cds001', 'bldgA', 'roomA101', '2024-07-01', 95, '卫生良好，物品摆放整齐。', 'dormadmin01'),
('cds002', 'bldgB', 'roomB205', '2024-07-01', 88, '阳台有杂物，已提醒。', 'dormadmin02'),
('cds003', 'bldgA', 'roomA102', '2024-07-01', 92, '整体不错。', 'dormadmin01'),
('cds004', 'bldgA', 'roomA101', '2024-06-01', 90, '卫生状况良好，继续保持。', 'dormadmin01'),
('cds005', 'bldgB', 'roomB205', '2024-06-01', 85, '需要改善卫生状况。', 'dormadmin02'),
('cds006', 'bldgA', 'roomA102', '2024-06-01', 93, '表现优秀。', 'dormadmin01');

-- ========================================
-- 创建视图和存储过程
-- ========================================

-- 创建用户详细信息视图
CREATE VIEW user_details AS
SELECT
    u.id,
    u.name,
    u.email,
    u.phone,
    u.role,
    u.college,
    u.major,
    u.dorm_building,
    u.room_number,
    u.emergency_contact_name,
    u.emergency_contact_phone,
    u.created_at,
    u.updated_at
FROM users u;

-- 创建房间占用情况视图
CREATE VIEW room_occupancy AS
SELECT
    r.id as room_id,
    r.room_number,
    db.name as dorm_building_name,
    r.floor,
    r.type,
    r.capacity,
    r.occupied_beds,
    (r.capacity - r.occupied_beds) as available_beds,
    ROUND((r.occupied_beds / r.capacity) * 100, 2) as occupancy_rate
FROM rooms r
JOIN dorm_buildings db ON r.dorm_building_id = db.id;

-- 创建维修请求详情视图
CREATE VIEW repair_request_details AS
SELECT
    rr.id,
    rr.student_id,
    rr.student_name,
    rr.room_number,
    rr.dorm_building,
    rr.description,
    rr.image_url,
    rr.contact,
    rr.status,
    rr.submitted_at,
    rr.assigned_to,
    rr.assigned_to_name,
    rr.created_at,
    rr.updated_at
FROM repair_requests rr;

-- ========================================
-- 创建触发器
-- ========================================

-- 触发器：更新房间占用床位数
DELIMITER //
CREATE TRIGGER update_room_occupied_beds_after_bed_insert
AFTER INSERT ON beds
FOR EACH ROW
BEGIN
    UPDATE rooms
    SET occupied_beds = (
        SELECT COUNT(*)
        FROM beds
        WHERE room_id = NEW.room_id AND status = '已入住'
    )
    WHERE id = NEW.room_id;
END//

CREATE TRIGGER update_room_occupied_beds_after_bed_update
AFTER UPDATE ON beds
FOR EACH ROW
BEGIN
    UPDATE rooms
    SET occupied_beds = (
        SELECT COUNT(*)
        FROM beds
        WHERE room_id = NEW.room_id AND status = '已入住'
    )
    WHERE id = NEW.room_id;

    -- 如果床位换了房间，也要更新旧房间的占用数
    IF OLD.room_id != NEW.room_id THEN
        UPDATE rooms
        SET occupied_beds = (
            SELECT COUNT(*)
            FROM beds
            WHERE room_id = OLD.room_id AND status = '已入住'
        )
        WHERE id = OLD.room_id;
    END IF;
END//

CREATE TRIGGER update_room_occupied_beds_after_bed_delete
AFTER DELETE ON beds
FOR EACH ROW
BEGIN
    UPDATE rooms
    SET occupied_beds = (
        SELECT COUNT(*)
        FROM beds
        WHERE room_id = OLD.room_id AND status = '已入住'
    )
    WHERE id = OLD.room_id;
END//

DELIMITER ;

-- ========================================
-- 数据库初始化完成
-- ========================================

-- 显示数据库创建完成信息
SELECT 'redhat数据库创建完成！' as message;
SELECT '数据库包含以下表:' as info;
SHOW TABLES;
